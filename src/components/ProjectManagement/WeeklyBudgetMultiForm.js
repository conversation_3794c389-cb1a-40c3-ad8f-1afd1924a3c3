import React, { Component } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  InputNumber,
  message,
  Icon,
  Card,
  Popconfirm,
} from 'antd';
import moment from 'moment';
import { weeklyBudgetAPI, supplierAPI } from '../../services/api';
import {
  SERVICE_TYPES,
  TAX_RATES,
  getWeekInfo,
  getMonthWeekInfoByAddition,
} from '../../utils/weeklyBudgetUtils';
import CustomWeekPicker from './CustomWeekPicker';

const { Option } = Select;

// 表单校验规则
const formRules = {
  weekRange: [
    { required: true, message: '请选择周期' },
  ],
  budgetItems: {
    serviceType: [
      { required: true, message: '请选择费用类型' },
    ],
    contractAmount: [
      { required: true, message: '请输入费用预算' },
      { type: 'number', min: 0.01, message: '费用预算必须大于0' },
    ],
    serviceContent: [
      { required: true, message: '请输入服务内容' },
    ],
    supplierId: [
      { required: true, message: '请选择供应商' },
    ],
  },
};

class WeeklyBudgetMultiForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      supplierOptions: [],
      budgetItems: [this.createEmptyBudgetItem()], // 初始化一个空的预算项目
      // selectedWeekRange: null, // 保存当前选择的周期
    };
  }

  componentDidMount() {
    this.loadSuppliers();
  }

  // 创建空的预算项目
  createEmptyBudgetItem = () => ({
    id: Date.now() + Math.random(), // 临时ID
    title: '',
    serviceType: undefined,
    serviceContent: '',
    contractAmount: undefined,
    taxRate: undefined,
    supplierId: undefined,
    remarks: '',
  });

  loadSuppliers = async () => {
    try {
      const response = await supplierAPI.getSuppliers({ status: 'active', pageSize: 1000 });
      if (response.success) {
        const supplierOptions = response.data.suppliers.map((supplier) => ({
          value: supplier.id,
          label: supplier.name,
          shortName: supplier.shortName,
          preferredTaxRate: supplier.preferredTaxRate,
        }));
        this.setState({ supplierOptions });
      }
    } catch (error) {
      console.error('Load suppliers failed:', error);
    }
  };

  // 获取服务类型标签
  getServiceTypeLabel = (serviceType) => {
    const type = SERVICE_TYPES.find((t) => t.value === serviceType);
    return type ? type.label : serviceType;
  };

  // 生成预算标题
  generateBudgetTitle = (weekRange, serviceType) => {
    if (!weekRange || !serviceType) {
      console.log('generateBudgetTitle: 缺少参数', { weekRange, serviceType });
      return '';
    }

    const monthWeekInfo = getMonthWeekInfoByAddition(weekRange);
    const projectName = this.props.projectInfo?.projectName || '项目';

    const title = `${projectName}-${monthWeekInfo.monthName}第${monthWeekInfo.weekOfMonth}周${this.getServiceTypeLabel(serviceType)}预算`;
    console.log('generateBudgetTitle: 生成标题', { title, projectName, monthWeekInfo, serviceType });

    return title;
  };

  // 处理周期变化
  handleWeekRangeChange = (week) => {
    console.log('handleWeekRangeChange: 周期变化', { week });
    const { budgetItems } = this.state;

    // 更新所有预算项目的标题
    const updatedItems = budgetItems.map((item) => ({
      ...item,
      title: item.serviceType ? this.generateBudgetTitle(week, item.serviceType) : '',
    }));

    console.log('handleWeekRangeChange: 更新后的预算项目', { updatedItems });

    this.setState({
      budgetItems: updatedItems,
      selectedWeekRange: week, // 保存当前选择的周期
    });
  };

  // 处理预算项目字段变化
  handleBudgetItemChange = (index, field, value) => {
    const { budgetItems, selectedWeekRange } = this.state;
    const updatedItems = [...budgetItems];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // 如果是服务类型变化，自动生成标题
    if (field === 'serviceType') {
      console.log('handleBudgetItemChange: 服务类型变化', { selectedWeekRange, value });
      if (selectedWeekRange) {
        updatedItems[index].title = this.generateBudgetTitle(selectedWeekRange, value);
      } else {
        console.log('handleBudgetItemChange: 没有选择周期，无法生成标题');
      }
    }

    // 如果是供应商变化，自动填入税率
    if (field === 'supplierId') {
      const { supplierOptions } = this.state;
      const selectedSupplier = supplierOptions.find((supplier) => supplier.value === value);
      if (selectedSupplier && selectedSupplier.preferredTaxRate) {
        updatedItems[index].taxRate = selectedSupplier.preferredTaxRate;
        message.info(`已自动填入供应商首选税率：${TAX_RATES.find((rate) => rate.value === selectedSupplier.preferredTaxRate).label}`);
      }
    }

    this.setState({ budgetItems: updatedItems });
  };

  // 添加预算项目
  handleAddBudgetItem = () => {
    const { budgetItems } = this.state;
    this.setState({
      budgetItems: [...budgetItems, this.createEmptyBudgetItem()],
    });
  };

  // 删除预算项目
  handleRemoveBudgetItem = (index) => {
    const { budgetItems } = this.state;
    if (budgetItems.length <= 1) {
      message.warning('至少需要保留一个预算项目');
      return;
    }

    const updatedItems = budgetItems.filter((_, i) => i !== index);
    this.setState({ budgetItems: updatedItems });
  };

  // 验证预算项目
  validateBudgetItems = () => {
    const { budgetItems } = this.state;

    for (let i = 0; i < budgetItems.length; i++) {
      const item = budgetItems[i];
      if (!item.serviceType) {
        message.error(`第${i + 1}个预算项目：请选择费用类型`);
        return false;
      }
      if (!item.contractAmount || item.contractAmount <= 0) {
        message.error(`第${i + 1}个预算项目：请输入有效的费用预算`);
        return false;
      }
      if (!item.serviceContent) {
        message.error(`第${i + 1}个预算项目：请输入服务内容`);
        return false;
      }
      if (!item.supplierId) {
        message.error(`第${i + 1}个预算项目：请选择供应商`);
        return false;
      }
    }

    return true;
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      // 验证预算项目
      if (!this.validateBudgetItems()) {
        return;
      }

      this.setState({ loading: true });

      try {
        const selectedWeek = values.weekRange;
        const weekStartDate = selectedWeek.clone().startOf('week');
        const weekEndDate = selectedWeek.clone().endOf('week');
        const weekInfo = getWeekInfo(selectedWeek);
        const monthWeekInfo = getMonthWeekInfoByAddition(selectedWeek);

        // 准备批量创建的数据
        const budgetDataList = this.state.budgetItems.map((item) => ({
          title: item.title,
          weekStartDate: weekStartDate.format('YYYY-MM-DD'),
          weekEndDate: weekEndDate.format('YYYY-MM-DD'),
          weekNumber: weekInfo.weekNumber,
          year: weekInfo.year,
          month: monthWeekInfo.month,
          weekOfMonth: monthWeekInfo.weekOfMonth,
          serviceType: item.serviceType,
          serviceContent: item.serviceContent,
          contractAmount: item.contractAmount,
          taxRate: item.taxRate,
          paidAmount: 0,
          supplierId: item.supplierId,
          remarks: item.remarks,
          status: 'created',
        }));

        // 批量创建预算
        const promises = budgetDataList.map((budgetData) =>
          weeklyBudgetAPI.createWeeklyBudget(this.props.projectId, budgetData));

        const responses = await Promise.all(promises);
        const successCount = responses.filter((response) => response.success).length;
        const failCount = responses.length - successCount;

        if (successCount > 0) {
          message.success(`成功创建 ${successCount} 个周预算${failCount > 0 ? `，失败 ${failCount} 个` : ''}`);
          const successData = responses.filter((r) => r.success).map((r) => r.data);
          this.props.onSubmit && this.props.onSubmit(successData);
        } else {
          message.error('创建周预算失败');
        }
      } catch (error) {
        console.error('Submit weekly budgets failed:', error);
        message.error('创建周预算失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  handleReset = () => {
    this.props.form.resetFields();
    this.setState({
      budgetItems: [this.createEmptyBudgetItem()],
      selectedWeekRange: null,
    });
  };

  handleCancel = () => {
    this.props.onCancel && this.props.onCancel();
  };


  render() {
    const { loading, budgetItems } = this.state;
    const { projectInfo } = this.props;
    const { getFieldDecorator } = this.props.form;

    // 项目执行周期限制
    const projectStartDate = projectInfo?.executionPeriod
      ? moment(projectInfo.executionPeriod[0])
      : null;
    const projectEndDate = projectInfo?.executionPeriod
      ? moment(projectInfo.executionPeriod[1])
      : null;

    return (
      <div style={{ padding: '16px' }}>
        <Form layout="inline" onSubmit={this.handleSubmit}>
          {/* 周期选择 */}
          <Card title="选择周期" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="周期" style={{ marginBottom: 0 }}>
                  {getFieldDecorator('weekRange', {
                    rules: formRules.weekRange,
                  })(
                    <CustomWeekPicker
                      style={{ width: '100%' }}
                      placeholder="请选择周期"
                      onChange={this.handleWeekRangeChange}
                      projectStartDate={projectStartDate}
                      projectEndDate={projectEndDate}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={16} style={{ display: 'flex', alignItems: 'end', paddingBottom: '4px' }}>
                <Button type="dashed" onClick={this.handleAddBudgetItem}>
                  <Icon type="plus" /> 添加预算项目
                </Button>
                <span style={{ marginLeft: 16, color: '#666' }}>
                  当前共 {budgetItems.length} 个预算项目
                </span>
              </Col>
            </Row>
          </Card>

          {/* 预算项目表格式布局 */}
          <Card title="预算项目列表" size="small" style={{ marginBottom: 16 }}>
            <div style={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse', fontSize: '12px' }}>
                <thead>
                  <tr style={{ backgroundColor: '#fafafa', borderBottom: '1px solid #e8e8e8' }}>
                    <th style={{ padding: '8px 4px', textAlign: 'center', width: '40px', border: '1px solid #e8e8e8' }}>序号</th>
                    <th style={{ padding: '8px 4px', textAlign: 'center', width: '100px', border: '1px solid #e8e8e8' }}>费用类型</th>
                    <th style={{ padding: '8px 4px', textAlign: 'center', width: '120px', border: '1px solid #e8e8e8' }}>供应商</th>
                    <th style={{ padding: '8px 4px', textAlign: 'center', minWidth: '200px', border: '1px solid #e8e8e8' }}>预算标题</th>
                    <th style={{ padding: '8px 4px', textAlign: 'center', width: '100px', border: '1px solid #e8e8e8' }}>费用预算</th>
                    <th style={{ padding: '8px 4px', textAlign: 'center', width: '80px', border: '1px solid #e8e8e8' }}>税率</th>
                    <th style={{ padding: '8px 4px', textAlign: 'center', minWidth: '150px', border: '1px solid #e8e8e8' }}>服务内容</th>
                    <th style={{ padding: '8px 4px', textAlign: 'center', width: '100px', border: '1px solid #e8e8e8' }}>备注</th>
                    <th style={{ padding: '8px 4px', textAlign: 'center', width: '60px', border: '1px solid #e8e8e8' }}>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {budgetItems.map((item, index) => (
                    <tr key={item.id} style={{ borderBottom: '1px solid #e8e8e8' }}>
                      <td style={{ padding: '4px', textAlign: 'center', border: '1px solid #e8e8e8' }}>
                        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{index + 1}</span>
                      </td>
                      <td style={{ padding: '4px', border: '1px solid #e8e8e8' }}>
                        <Select
                          size="small"
                          placeholder="选择类型"
                          value={item.serviceType}
                          onChange={(value) => this.handleBudgetItemChange(index, 'serviceType', value)}
                          style={{ width: '100%' }}
                        >
                          {SERVICE_TYPES.map((type) => (
                            <Option key={type.value} value={type.value}>
                              {type.label}
                            </Option>
                          ))}
                        </Select>
                      </td>
                      <td style={{ padding: '4px', border: '1px solid #e8e8e8' }}>
                        <Select
                          size="small"
                          placeholder="选择供应商"
                          allowClear
                          showSearch
                          value={item.supplierId}
                          onChange={(value) => this.handleBudgetItemChange(index, 'supplierId', value)}
                          style={{ width: '100%' }}
                          filterOption={(input, option) =>
                            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {this.state.supplierOptions.map((supplier) => (
                            <Option key={supplier.value} value={supplier.value}>
                              {supplier.label}
                            </Option>
                          ))}
                        </Select>
                      </td>
                      <td style={{ padding: '4px', border: '1px solid #e8e8e8' }}>
                        <Input
                          size="small"
                          placeholder="自动生成"
                          value={item.title}
                          onChange={(e) => this.handleBudgetItemChange(index, 'title', e.target.value)}
                          style={{ width: '100%' }}
                        />
                      </td>
                      <td style={{ padding: '4px', border: '1px solid #e8e8e8' }}>
                        <InputNumber
                          size="small"
                          placeholder="预算金额"
                          style={{ width: '100%' }}
                          min={0}
                          step={100}
                          value={item.contractAmount}
                          onChange={(value) => this.handleBudgetItemChange(index, 'contractAmount', value)}
                          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                          parser={(value) => value.replace(/,/g, '')}
                        />
                      </td>
                      <td style={{ padding: '4px', border: '1px solid #e8e8e8' }}>
                        <Select
                          size="small"
                          placeholder="税率"
                          value={item.taxRate}
                          onChange={(value) => this.handleBudgetItemChange(index, 'taxRate', value)}
                          style={{ width: '100%' }}
                        >
                          {TAX_RATES.map((rate) => (
                            <Option key={rate.value} value={rate.value}>
                              {rate.label}
                            </Option>
                          ))}
                        </Select>
                      </td>
                      <td style={{ padding: '4px', border: '1px solid #e8e8e8' }}>
                        <Input.TextArea
                          size="small"
                          placeholder="服务内容"
                          rows={1}
                          value={item.serviceContent}
                          onChange={(e) => this.handleBudgetItemChange(index, 'serviceContent', e.target.value)}
                          style={{ width: '100%', resize: 'none' }}
                        />
                      </td>
                      <td style={{ padding: '4px', border: '1px solid #e8e8e8' }}>
                        <Input
                          size="small"
                          placeholder="备注"
                          value={item.remarks}
                          onChange={(e) => this.handleBudgetItemChange(index, 'remarks', e.target.value)}
                          style={{ width: '100%' }}
                        />
                      </td>
                      <td style={{ padding: '4px', textAlign: 'center', border: '1px solid #e8e8e8' }}>
                        {budgetItems.length > 1 && (
                          <Popconfirm
                            title="确定删除？"
                            onConfirm={() => this.handleRemoveBudgetItem(index)}
                            okText="确定"
                            cancelText="取消"
                          >
                            <Button type="link" danger size="small" style={{ padding: '0 4px' }}>
                              <Icon type="delete" />
                            </Button>
                          </Popconfirm>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 汇总信息 */}
            <div style={{ marginTop: 16, padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
              <Row gutter={16}>
                <Col span={6}>
                  <strong>预算项目总数：</strong>
                  <span style={{ color: '#1890ff' }}>{budgetItems.length} 个</span>
                </Col>
                <Col span={6}>
                  <strong>预算总金额：</strong>
                  <span style={{ color: '#52c41a' }}>
                    ¥{budgetItems.reduce((sum, item) => sum + (item.contractAmount || 0), 0)
                    .toLocaleString()}
                  </span>
                </Col>
                <Col span={6}>
                  <strong>已填写项目：</strong>
                  <span style={{ color: '#722ed1' }}>
                    {budgetItems.filter((item) =>
                      item.serviceType && item.supplierId &&
                      item.contractAmount && item.serviceContent).length} 个
                  </span>
                </Col>
                <Col span={6}>
                  <strong>待完善项目：</strong>
                  <span style={{ color: '#fa8c16' }}>
                    {budgetItems.filter((item) =>
                      !item.serviceType || !item.supplierId ||
                      !item.contractAmount || !item.serviceContent).length} 个
                  </span>
                </Col>
              </Row>
            </div>
          </Card>

          {/* 操作按钮 */}
          <Row>
            <Col span={24} style={{ textAlign: 'center', marginTop: 16 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                style={{ marginRight: 16 }}
              >
                <Icon type="plus" />
                创建预算 ({budgetItems.length}个)
              </Button>
              <Button
                onClick={this.handleReset}
                size="large"
                style={{ marginRight: 16 }}
              >
                <Icon type="reload" />
                重置表单
              </Button>
              <Button
                onClick={this.handleCancel}
                size="large"
              >
                <Icon type="close" />
                取消
              </Button>
            </Col>
          </Row>
        </Form>
      </div>
    );
  }
}

export default Form.create()(WeeklyBudgetMultiForm);

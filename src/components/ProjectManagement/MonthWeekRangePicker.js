import React, { Component } from 'react';
import { Select, DatePicker, Row, Col } from 'antd';
import moment from 'moment';
import { generateMonthWeekOptions } from '../../utils/weeklyBudgetUtils';

const { Option } = Select;
const { MonthPicker } = DatePicker;

class MonthWeekRangePicker extends Component {
  constructor(props) {
    super(props);

    // 从 value 中解析初始状态
    const initialState = this.parseValue(props.value);

    this.state = {
      selectedMonth: initialState.selectedMonth,
      selectedWeek: initialState.selectedWeek,
      weekOptions: initialState.weekOptions,
    };
  }

  componentDidUpdate(prevProps) {
    // 当外部 value 变化时，更新内部状态
    if (prevProps.value !== this.props.value) {
      const newState = this.parseValue(this.props.value);
      this.setState(newState);
    }
  }

  // 解析 value 并生成对应的状态
  parseValue = (value) => {
    if (!value || !value.weekStartDate) {
      // 如果没有值，返回空状态，让用户主动选择
      return {
        selectedMonth: null,
        selectedWeek: null,
        weekOptions: [],
      };
    }

    const startDate = moment(value.weekStartDate);
    const year = startDate.year();
    const month = startDate.month() + 1;
    const selectedMonth = moment({ year, month: month - 1 });

    // 生成该月的周期选项
    const weekOptions = generateMonthWeekOptions(
      year,
      month,
      this.props.projectStartDate,
      this.props.projectEndDate,
    );

    // 找到对应的周期选项
    const selectedOption = weekOptions.find((option) =>
      option.weekStartDate === value.weekStartDate &&
      option.weekEndDate === value.weekEndDate);

    return {
      selectedMonth,
      selectedWeek: selectedOption ? selectedOption.value : null,
      weekOptions,
    };
  };

  // 处理月份选择
  handleMonthChange = (monthMoment) => {
    console.log('handleMonthChange called with:', monthMoment);

    if (!monthMoment) {
      this.setState({
        selectedMonth: null,
        selectedWeek: null,
        weekOptions: [],
      });
      this.triggerChange(null);
      return;
    }

    const year = monthMoment.year();
    const month = monthMoment.month() + 1;

    console.log('Generating options for:', { year, month });

    // 生成该月的周期选项
    const weekOptions = generateMonthWeekOptions(
      year,
      month,
      this.props.projectStartDate,
      this.props.projectEndDate,
    );

    console.log('Generated weekOptions:', weekOptions);

    this.setState({
      selectedMonth: monthMoment,
      selectedWeek: null, // 重置周期选择
      weekOptions,
    }, () => {
      console.log('State updated:', this.state);
    });

    // 清空选择
    this.triggerChange(null);
  };

  // 处理周期选择
  handleWeekChange = (weekValue) => {
    if (!weekValue) {
      this.setState({ selectedWeek: null });
      this.triggerChange(null);
      return;
    }

    const selectedOption = this.state.weekOptions.find((option) => option.value === weekValue);
    if (!selectedOption) return;

    this.setState({ selectedWeek: weekValue });

    // 构造返回值，包含完整的周期信息
    const result = {
      weekStartDate: selectedOption.weekStartDate,
      weekEndDate: selectedOption.weekEndDate,
      year: selectedOption.year,
      month: selectedOption.month,
      weekOfMonth: selectedOption.weekNum,
      displayText: selectedOption.label,
      // 为了兼容现有代码，也提供 moment 对象
      startMoment: moment(selectedOption.weekStartDate),
      endMoment: moment(selectedOption.weekEndDate),
    };

    this.triggerChange(result);
  };

  // 触发 onChange 事件
  triggerChange = (value) => {
    if (this.props.onChange) {
      this.props.onChange(value);
    }
  };

  render() {
    const { selectedMonth, selectedWeek, weekOptions } = this.state;
    const { style, disabled } = this.props;

    return (
      <div style={style}>
        <Row gutter={8}>
          <Col span={12}>
            <MonthPicker
              value={selectedMonth}
              onChange={this.handleMonthChange}
              placeholder="选择月份"
              style={{ width: '100%' }}
              disabled={disabled}
              allowClear
            />
          </Col>
          <Col span={12}>
            <Select
              value={selectedWeek}
              onChange={this.handleWeekChange}
              placeholder="选择周期"
              style={{ width: '100%' }}
              disabled={disabled || !selectedMonth}
              allowClear
              showSearch={false}
            >
              {weekOptions.map((option) => (
                <Option
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                >
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </div>
    );
  }
}

export default MonthWeekRangePicker;
